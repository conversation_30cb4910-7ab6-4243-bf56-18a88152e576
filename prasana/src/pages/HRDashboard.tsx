import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Download, 
  Award, 
  Calendar, 
  MapPin, 
  Mail, 
  Phone,
  Building,
  Clock,
  UserCheck,
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  Eye,
  X
} from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { 
  fetchHREmployeeProfiles,
  filterHREmployeeProfiles,
  getHRFilterOptions,
  exportEmployeeDataToCSV
} from '../data/supabaseHR';
import { 
  HREmployeeProfile, 
  HRFilterOptions, 
  HRSearchOptions, 
  HRSortOptions 
} from '../types/hr';
import HREmployeeDetailModal from '../components/HREmployeeDetailModal';
import ProfilePicture from '../components/ProfilePicture';

const HRDashboard: React.FC = () => {
  const { user, isSuperAdmin } = useUser();
  const [employees, setEmployees] = useState<HREmployeeProfile[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<HREmployeeProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<HREmployeeProfile | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  
  // Search and filter state
  const [searchOptions, setSearchOptions] = useState<HRSearchOptions>({
    query: '',
    fields: ['name', 'email', 'designation', 'department', 'employee_id']
  });
  
  const [filterOptions, setFilterOptions] = useState<HRFilterOptions>({});
  const [sortOptions, setSortOptions] = useState<HRSortOptions>({
    field: 'full_name',
    direction: 'asc'
  });
  
  const [availableFilters, setAvailableFilters] = useState({
    departments: [] as string[],
    teams: [] as string[],
    employment_types: [] as string[],
    employment_statuses: [] as string[],
    locations: [] as string[],
    managers: [] as string[]
  });

  // Access control - only Super Admins can access this view
  if (!isSuperAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-sm border">
          <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">
            This HR Dashboard is restricted to Super Admin users only.
          </p>
          <p className="text-sm text-gray-500">
            Contact your system administrator if you need access to HR management features.
          </p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    loadEmployeeData();
  }, []);

  useEffect(() => {
    applyFiltersAndSearch();
  }, [employees, searchOptions, filterOptions, sortOptions]);

  const loadEmployeeData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await fetchHREmployeeProfiles();
      setEmployees(data);
      
      // Set up available filter options
      const filters = getHRFilterOptions(data);
      setAvailableFilters(filters);
      
    } catch (err: any) {
      console.error('Error loading HR employee data:', err);
      setError(err.message || 'Failed to load employee data');
    } finally {
      setLoading(false);
    }
  };

  const applyFiltersAndSearch = () => {
    let filtered = filterHREmployeeProfiles(employees, filterOptions, searchOptions);
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortOptions.field];
      const bValue = b[sortOptions.field];
      
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;
      
      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }
      
      return sortOptions.direction === 'asc' ? comparison : -comparison;
    });
    
    setFilteredEmployees(filtered);
  };

  const handleSort = (field: keyof HREmployeeProfile) => {
    setSortOptions(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleExport = () => {
    const fields: (keyof HREmployeeProfile)[] = [
      'employee_id', 'full_name', 'email', 'designation', 'department', 
      'team_name', 'employment_type', 'joining_date', 'employment_status',
      'active_badges_count', 'years_of_service', 'total_leave_balance'
    ];
    
    const csvData = exportEmployeeDataToCSV(filteredEmployees, fields);
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hr_employee_data_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const clearFilters = () => {
    setFilterOptions({});
    setSearchOptions(prev => ({ ...prev, query: '' }));
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'terminated': return 'bg-red-100 text-red-800';
      case 'on_leave': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mb-6"></div>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-red-800">Error Loading HR Data</h3>
                <p className="text-red-600 mt-1">{error}</p>
                <button 
                  onClick={loadEmployeeData}
                  className="mt-3 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-black gradient-text flex items-center gap-3 header-section animate-scaleIn">
                <Users className="h-8 w-8 text-blue-600" />
                HR Dashboard
              </h1>
              <p className="text-gray-600 mt-2">
                Comprehensive employee management and analytics
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleExport}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
              >
                <Download size={18} />
                Export Data
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-3xl font-bold text-gray-900">{employees.length}</p>
              </div>
              <Users className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Employees</p>
                <p className="text-3xl font-bold text-green-600">
                  {employees.filter(e => e.employment_status === 'active').length}
                </p>
              </div>
              <UserCheck className="h-12 w-12 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Badges</p>
                <p className="text-3xl font-bold text-purple-600">
                  {employees.reduce((sum, e) => sum + e.active_badges_count, 0)}
                </p>
              </div>
              <Award className="h-12 w-12 text-purple-600" />
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Departments</p>
                <p className="text-3xl font-bold text-orange-600">
                  {availableFilters.departments.length}
                </p>
              </div>
              <Building className="h-12 w-12 text-orange-600" />
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <input
                  type="text"
                  placeholder="Search employees by name, email, designation..."
                  className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={searchOptions.query}
                  onChange={(e) => setSearchOptions(prev => ({ ...prev, query: e.target.value }))}
                />
              </div>
            </div>
            
            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
            >
              <Filter size={18} />
              Filters
              {showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            
            {/* Clear Filters */}
            {(Object.keys(filterOptions).length > 0 || searchOptions.query) && (
              <button
                onClick={clearFilters}
                className="text-red-600 hover:text-red-800 px-4 py-2 rounded-lg border border-red-200 hover:bg-red-50"
              >
                Clear All
              </button>
            )}
          </div>
          
          {/* Filter Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <select
                value={filterOptions.department || ''}
                onChange={(e) => setFilterOptions(prev => ({ ...prev, department: e.target.value || undefined }))}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Departments</option>
                {availableFilters.departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
              
              <select
                value={filterOptions.team || ''}
                onChange={(e) => setFilterOptions(prev => ({ ...prev, team: e.target.value || undefined }))}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Teams</option>
                {availableFilters.teams.map(team => (
                  <option key={team} value={team}>{team}</option>
                ))}
              </select>
              
              <select
                value={filterOptions.employment_type || ''}
                onChange={(e) => setFilterOptions(prev => ({ ...prev, employment_type: e.target.value || undefined }))}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Employment Types</option>
                {availableFilters.employment_types.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              
              <select
                value={filterOptions.employment_status || ''}
                onChange={(e) => setFilterOptions(prev => ({ ...prev, employment_status: e.target.value || undefined }))}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Statuses</option>
                {availableFilters.employment_statuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="mb-4 text-sm text-gray-600">
          Showing {filteredEmployees.length} of {employees.length} employees
        </div>

        {/* Employee Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Photo
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('employee_id')}
                  >
                    Employee ID
                    {sortOptions.field === 'employee_id' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('full_name')}
                  >
                    Name
                    {sortOptions.field === 'full_name' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('designation')}
                  >
                    Position
                    {sortOptions.field === 'designation' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('team_name')}
                  >
                    Team
                    {sortOptions.field === 'team_name' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('active_badges_count')}
                  >
                    Badges
                    {sortOptions.field === 'active_badges_count' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('employment_status')}
                  >
                    Status
                    {sortOptions.field === 'employment_status' && (
                      sortOptions.direction === 'asc' ? <ChevronUp className="inline ml-1" size={14} /> : <ChevronDown className="inline ml-1" size={14} />
                    )}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEmployees.map((employee) => (
                  <tr key={employee.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <ProfilePicture
                        userId={employee.user_id || employee.id}
                        name={employee.full_name}
                        email={employee.email}
                        profilePictureUrl={employee.profile_picture_url}
                        size="sm"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {employee.employee_id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{employee.full_name}</div>
                        <div className="text-sm text-gray-500">{employee.designation}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center gap-1">
                        <Mail size={14} className="text-gray-400" />
                        {employee.email}
                      </div>
                      {employee.phone && (
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          <Phone size={14} className="text-gray-400" />
                          {employee.phone}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{employee.designation}</div>
                      <div className="text-sm text-gray-500">{employee.department}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{employee.team_name || 'No Team'}</div>
                      {employee.manager_name && (
                        <div className="text-sm text-gray-500">Manager: {employee.manager_name}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-1">
                        <Award size={16} className="text-purple-600" />
                        <span className="text-sm font-medium text-purple-600">
                          {employee.active_badges_count}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(employee.employment_status)}`}>
                        {employee.employment_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedEmployee(employee);
                          setShowDetailModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                      >
                        <Eye size={16} />
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Empty State */}
        {filteredEmployees.length === 0 && !loading && (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm border">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No employees found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}

        {/* Employee Detail Modal */}
        {showDetailModal && selectedEmployee && (
          <HREmployeeDetailModal
            employee={selectedEmployee}
            onClose={() => {
              setShowDetailModal(false);
              setSelectedEmployee(null);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default HRDashboard;
