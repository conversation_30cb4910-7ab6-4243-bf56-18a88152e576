import { Teams } from '../types/team';

// Helper function to get local profile image path - REMOVED
// const getProfileImage = (name: string) => {
//     // Convert name to lowercase and replace spaces with underscores
//     const imageName = name.toLowerCase().replace(/\s+/g, '_');
//     return `/profiles/${imageName}.jpg`;  // Assuming images are in JPG format
// };

export const teams: Teams = {
    athena: {
        name: 'ATHEN<PERSON>',
        sdm: {
            name: '<PERSON> <PERSON>',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/sri_ram.png',
            uuid: '0119e6a3-79cb-42a5-bc59-b5802fc25d72',
        },
        tdm: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/selvandrane.png',
            uuid: '',
        },
        cxm: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/mahesh.png',
            uuid: '',
        },
        members: [
            {
                name: '<PERSON> <PERSON><PERSON>',
                role: 'Associate Trainee',
                avatar: '/profiles/shri.png',
                uuid: '',
            },
            {
                name: 'Prasanna',
                role: 'Client Experience Manager',
                avatar: '/profiles/prasana.png',
                uuid: '',
            },
            {
                name: 'Kavin Mithra V',
                role: 'Associate Trainee',
                avatar: '/profiles/default_avatar.png',
                uuid: '',
            },
            {
                name: 'Fazeela',
                role: 'Associate Trainee',
                avatar: '/profiles/fazzela.png',
                uuid: '',
            },
            {
                name: 'Sivaranjani',
                role: 'Associate Trainee',
                avatar: '/profiles/sivaranjani.png',
                uuid: '',
            }
        ]
    },
    dynamix: {
        name: 'DYNAMIX',
        sdm: {
            name: 'Yuvaraj',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/yuvaraj.png',
            uuid: '',
        },
        cxm: {
            name: 'Purushoth',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/pourushoth.png',
            uuid: '',
        },
        tdm: {
            name: 'Kiyshore K',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/kiyshor.png',
            uuid: '',
        },
        members: [
            {
                name: 'Nithish K',
                role: 'Associate Trainee',
                avatar: '/profiles/default_avatar.png',
                uuid: '',
            },
            {
                name: 'Nitesh S',
                role: 'Associate Trainee',
                avatar: '/profiles/default_avatar.png',
                uuid: '',
            },
            {
                name: 'S. Keerthipriya',
                role: 'Associate Trainee',
                avatar: '/profiles/default_avatar.png',
                uuid: '',
            }
        ]
    },
    nexus: {
        name: 'NEXUS',
        sdm: {
            name: 'Eashwara Prasadh',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/eashwara_prasadh.jpg',
            uuid: '3eb1e2ce-7178-4e1a-9d3f-45ba1d1ddc12',
        },
        cxm: {
            name: 'Darshan K',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/darshan.png',
            uuid: '',
        },
        tdm: {
            name: 'Yusuf Fayas',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/yusuf.png',
            uuid: '',
        },
        members: [
            {
                name: 'Gaushik Adhiban E',
                role: 'Associate Trainee',
                avatar: '/profiles/gaushik.png',
                uuid: '',
            },
            {
                name: 'Hariharan B',
                role: 'Associate Trainee',
                avatar: '/profiles/default_avatar.png',
                uuid: '',
            },
            {
                name: 'Sakthivel N',
                role: 'Associate Trainee',
                avatar: '/profiles/sakthivel.png',
                uuid: '',
            }
        ]
    },
    titan: {
        name: 'TITAN',
        sdm: {
            name: 'Aamina Begam T',
            role: 'Service Delivery Manager',
            designation: 'Service Delivery Manager',
            avatar: '/profiles/aamina.png',
            uuid: '41d8e0c5-077f-4227-a4cb-6098ffc44671',
        },
        tdm: {
            name: 'TBD',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/default_avatar.png',
            uuid: '',
        },
        cxm: {
            name: 'TBD',
            role: 'Client Experience Manager',
            designation: 'Client Experience Manager',
            avatar: '/profiles/default_avatar.png',
            uuid: '',
        },
        members: [
            {
                name: 'Yamini',
                role: 'Associate Trainee',
                avatar: '/profiles/yamini.png',
                uuid: '',
            }
        ]
    },
    development: {
        name: 'DEVELOPMENT',
        tdm: {
            name: 'Gowtham Kollati',
            role: 'Technical Delivery Manager',
            designation: 'Technical Delivery Manager',
            avatar: '/profiles/kollati.png',
            uuid: '',
        },
        members: [
            {
                name: 'Praveen Dommeti',
                role: 'Associate Trainee',
                avatar: '/profiles/praveen.png',
                uuid: '',
            }
        ]
    }
};