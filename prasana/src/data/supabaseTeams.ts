import { supabase } from '../supabaseClient';
import { TeamMember, TeamData, Teams } from '../types/team';
import { teams as staticTeams } from './teams';

// Helper function to normalize names for comparison
const normalizeName = (name: string): string => {
  return name.toLowerCase().trim().replace(/\s+/g, ' ');
};

// Helper function to get avatar path from static teams data
const getAvatarFromStaticTeams = (name: string, teamName: string): string => {
  const teamKey = teamName.toLowerCase() as keyof typeof staticTeams;
  const team = staticTeams[teamKey];

  if (!team) {
    console.log(`No team found for ${teamName}, using fallback avatar for ${name}`);
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
  }

  const normalizedName = normalizeName(name);

  // Check leadership roles first with normalized names
  if (team.sdm?.name && normalizeName(team.sdm.name) === normalizedName) {
    console.log(`Found SDM avatar for ${name}: ${team.sdm.avatar}`);
    return team.sdm.avatar || '';
  }
  if (team.tdm?.name && normalizeName(team.tdm.name) === normalizedName) {
    console.log(`Found TDM avatar for ${name}: ${team.tdm.avatar}`);
    return team.tdm.avatar || '';
  }
  if (team.cxm?.name && normalizeName(team.cxm.name) === normalizedName) {
    console.log(`Found CXM avatar for ${name}: ${team.cxm.avatar}`);
    return team.cxm.avatar || '';
  }

  // Check regular members with normalized names
  const member = team.members?.find(m => m.name && normalizeName(m.name) === normalizedName);
  if (member?.avatar) {
    console.log(`Found member avatar for ${name}: ${member.avatar}`);
    return member.avatar;
  }

  // Try partial matching for cases where names might be slightly different
  const partialMatch = team.members?.find(m => {
    if (!m.name) return false;
    const memberNormalized = normalizeName(m.name);
    const nameNormalized = normalizeName(name);
    return memberNormalized.includes(nameNormalized) || nameNormalized.includes(memberNormalized);
  });
  if (partialMatch?.avatar) {
    console.log(`Found partial match avatar for ${name}: ${partialMatch.avatar}`);
    return partialMatch.avatar;
  }

  // Check leadership with partial matching
  if (team.sdm?.name && (normalizeName(team.sdm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.sdm.name)))) {
    console.log(`Found SDM partial match avatar for ${name}: ${team.sdm.avatar}`);
    return team.sdm.avatar || '';
  }
  if (team.tdm?.name && (normalizeName(team.tdm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.tdm.name)))) {
    console.log(`Found TDM partial match avatar for ${name}: ${team.tdm.avatar}`);
    return team.tdm.avatar || '';
  }
  if (team.cxm?.name && (normalizeName(team.cxm.name).includes(normalizedName) || normalizedName.includes(normalizeName(team.cxm.name)))) {
    console.log(`Found CXM partial match avatar for ${name}: ${team.cxm.avatar}`);
    return team.cxm.avatar || '';
  }

  // Fallback to generated avatar
  console.log(`No avatar match found for ${name} in team ${teamName}, using fallback`);
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
};

// Fetch all team members from database
export const fetchTeamMembers = async (): Promise<TeamMember[]> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select(`
        id,
        user_id,
        first_name,
        last_name,
        email,
        designation,
        department,
        status,
        team_id,
        teams!inner(
          id,
          name,
          description
        )
      `)
      .eq('status', 'active');

    if (error) {
      console.error('Error fetching team members:', error);
      throw error;
    }

    return data?.map(employee => {
      const fullName = `${employee.first_name} ${employee.last_name}`.trim();
      const teamName = employee.teams?.name || employee.department;
      return {
        id: employee.id,
        name: fullName,
        role: employee.designation as any,
        designation: employee.designation,
        team: teamName,
        avatar: getAvatarFromStaticTeams(fullName, teamName),
        uuid: employee.user_id,
        isLeadership: ['Service Delivery Manager', 'Technical Account Manager', 'Client Experience Manager'].includes(employee.designation)
      };
    }) || [];
  } catch (error) {
    console.error('Error in fetchTeamMembers:', error);
    return [];
  }
};

// Fetch teams with their members organized by role
export const fetchTeamsWithMembers = async (): Promise<Teams> => {
  try {
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('is_active', true);

    if (teamsError) {
      console.error('Error fetching teams:', teamsError);
      throw teamsError;
    }

    const { data: employeesData, error: employeesError } = await supabase
      .from('employees')
      .select(`
        id,
        user_id,
        first_name,
        last_name,
        email,
        designation,
        department,
        status,
        team_id
      `)
      .eq('status', 'active');

    if (employeesError) {
      console.error('Error fetching employees:', employeesError);
      throw employeesError;
    }

    const teams: Teams = {};

    teamsData?.forEach(team => {
      const teamMembers = employeesData?.filter(emp => emp.team_id === team.id) || [];

      // Find leadership roles
      const sdm = teamMembers.find(emp => emp.designation === 'Service Delivery Manager');
      const tdm = teamMembers.find(emp => emp.designation === 'Technical Account Manager');
      const cxm = teamMembers.find(emp => emp.designation === 'Client Experience Manager');

      // Get regular members (non-leadership)
      const members = teamMembers.filter(emp =>
        !['Service Delivery Manager', 'Technical Account Manager', 'Client Experience Manager'].includes(emp.designation)
      );

      const createTeamMember = (emp: any): TeamMember => {
        const fullName = `${emp.first_name} ${emp.last_name}`.trim();
        return {
          id: emp.id,
          name: fullName,
          role: emp.designation as any,
          designation: emp.designation,
          team: team.name,
          avatar: getAvatarFromStaticTeams(fullName, team.name),
          uuid: emp.user_id,
          isLeadership: ['Service Delivery Manager', 'Technical Account Manager', 'Client Experience Manager'].includes(emp.designation)
        };
      };

      const teamKey = team.name.toLowerCase();
      teams[teamKey] = {
        name: team.name,
        sdm: sdm ? createTeamMember(sdm) : {
          name: 'TBD',
          role: 'Service Delivery Manager',
          designation: 'Service Delivery Manager',
          team: team.name,
          avatar: '/profiles/default_avatar.png',
          isLeadership: true
        },
        tdm: tdm ? createTeamMember(tdm) : {
          name: 'TBD',
          role: 'Technical Account Manager',
          designation: 'Technical Account Manager',
          team: team.name,
          avatar: '/profiles/default_avatar.png',
          isLeadership: true
        },
        cxm: cxm ? createTeamMember(cxm) : {
          name: 'TBD',
          role: 'Client Experience Manager',
          designation: 'Client Experience Manager',
          team: team.name,
          avatar: '/profiles/default_avatar.png',
          isLeadership: true
        },
        members: members.map(createTeamMember)
      };
    });

    return teams;
  } catch (error) {
    console.error('Error in fetchTeamsWithMembers:', error);
    return {};
  }
};

// Fetch project members for a specific project
export const fetchProjectMembers = async (projectId: string): Promise<TeamMember[]> => {
  try {
    const { data, error } = await supabase
      .from('project_members')
      .select(`
        id,
        role,
        start_date,
        is_active,
        employees!inner(
          id,
          user_id,
          first_name,
          last_name,
          email,
          designation,
          department,
          teams(
            name
          )
        )
      `)
      .eq('project_id', projectId)
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching project members:', error);
      throw error;
    }

    return data?.map(member => {
      const fullName = `${member.employees.first_name} ${member.employees.last_name}`.trim();
      const teamName = member.employees.teams?.name || member.employees.department;
      return {
        id: member.employees.id,
        name: fullName,
        role: member.role as any,
        designation: member.employees.designation,
        team: teamName,
        avatar: getAvatarFromStaticTeams(fullName, teamName),
        uuid: member.employees.user_id,
        isLeadership: ['Service Delivery Manager', 'Technical Account Manager', 'Client Experience Manager'].includes(member.employees.designation)
      };
    }) || [];
  } catch (error) {
    console.error('Error in fetchProjectMembers:', error);
    return [];
  }
};

// Add a member to a project
export const addProjectMember = async (projectId: string, employeeId: string, role: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('project_members')
      .insert({
        project_id: projectId,
        employee_id: employeeId,
        role: role,
        is_active: true,
        start_date: new Date().toISOString().split('T')[0],
        added_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error adding project member:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in addProjectMember:', error);
    return false;
  }
};

// Remove a member from a project
export const removeProjectMember = async (projectId: string, employeeId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('project_members')
      .update({ is_active: false })
      .eq('project_id', projectId)
      .eq('employee_id', employeeId);

    if (error) {
      console.error('Error removing project member:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in removeProjectMember:', error);
    return false;
  }
};

// Fetch projects for a specific team
export const fetchTeamProjects = async (teamName: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('team', teamName.toUpperCase())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching team projects:', error);
      throw error;
    }

    // Map snake_case to camelCase for frontend compatibility
    const mappedData = data?.map(project => ({
      ...project,
      teamMembers: project.team_members || [],
      clientName: project.client_name || project.clientName,
      clientLogo: project.client_logo || project.clientLogo,
      clientDomain: project.client_domain || project.clientDomain,
      clientCategory: project.client_category || project.clientCategory,
      startDate: project.start_date || project.startDate,
      endDate: project.end_date || project.endDate,
      createdAt: project.created_at || project.createdAt,
      updatedAt: project.updated_at || project.updatedAt
    })) || [];

    return mappedData;
  } catch (error) {
    console.error('Error in fetchTeamProjects:', error);
    return [];
  }
};
