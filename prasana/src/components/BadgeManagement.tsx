import React, { useState, useEffect } from 'react';
import { BadgeType, EmployeeBadge, BadgeAssignment } from '../types/badge';
import { useUser } from '../contexts/UserContext';
import {
  fetchBadgeTypes,
  assignBadgeToEmployee,
  revokeBadgeFromEmployee,
  fetchEmployeeBadges,
  checkEmployeeHasBadge
} from '../data/supabaseBadges';
import { Plus, X, Award, Calendar } from 'lucide-react';
import BadgeImage from './BadgeImage';
import { hrmsService } from '../services/hrmsService';

interface BadgeManagementProps {
  userId: string;
  userName: string;
  onClose: () => void;
}

const BadgeManagement: React.FC<BadgeManagementProps> = ({ userId, userName, onClose }) => {
  const [availableBadges, setAvailableBadges] = useState<BadgeType[]>([]);
  const [userBadges, setUserBadges] = useState<EmployeeBadge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expiryDays, setExpiryDays] = useState<number>(90);
  const [notes, setNotes] = useState<string>('');
  const { user, isAdmin, isSuperAdmin } = useUser();
  const [currentEmployee, setCurrentEmployee] = useState<any>(null);

  useEffect(() => {
    loadBadges();
    loadCurrentEmployee();
  }, [userId]);

  const loadCurrentEmployee = async () => {
    try {
      const employee = await hrmsService.getCurrentEmployee();
      setCurrentEmployee(employee);
    } catch (error) {
      console.error('Error loading current employee:', error);
    }
  };

  const loadBadges = async () => {
    try {
      setLoading(true);
      const [badges, userBadgeAssignments] = await Promise.all([
        fetchBadgeTypes(),
        fetchEmployeeBadges(userId)
      ]);
      setAvailableBadges(badges);
      setUserBadges(userBadgeAssignments);
    } catch (err: any) {
      setError(err.message);
      console.error('Error loading badges:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignBadge = async (badgeId: string) => {
    if (!isAdmin && !isSuperAdmin) return;

    try {
      // Check if user already has this badge
      const hasBadge = await checkEmployeeHasBadge(userId, badgeId);
      if (hasBadge) {
        setError('Employee already has this badge!');
        return;
      }

      const assignment: BadgeAssignment = {
        employee_id: userId,
        badge_type_id: badgeId,
        notes: notes,
        expiry_date: new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };

      // Use the current employee's ID for assignment tracking
      const success = await assignBadgeToEmployee(assignment, currentEmployee?.id || '');
      if (success) {
        setError(null);
        setNotes('');
        await loadBadges(); // Refresh the badges
      } else {
        setError('Failed to assign badge');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Error assigning badge:', err);
    }
  };

  const handleRemoveBadge = async (badgeId: string, badgeName: string) => {
    if (!isAdmin && !isSuperAdmin) return;

    if (!confirm(`Are you sure you want to revoke the "${badgeName}" badge?`)) {
      return;
    }

    try {
      // Use the current user's employee ID for revocation tracking
      const success = await revokeBadgeFromEmployee(badgeId, user?.employee_id || '', 'Revoked by admin');
      if (success) {
        setError(null);
        await loadBadges(); // Refresh the badges
      } else {
        setError('Failed to revoke badge');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Error revoking badge:', err);
    }
  };

  const calculateDaysRemaining = (expiryDate: string): number => {
    if (!expiryDate) return -1; // No expiry
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (!isAdmin && !isSuperAdmin) {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Manage Badges - {userName}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="space-y-6">
          {/* Badge Assignment Controls */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Badge Assignment Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Validity (Days)</label>
                <input
                  type="number"
                  value={expiryDays}
                  onChange={(e) => setExpiryDays(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  min="1"
                  max="365"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <input
                  type="text"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Reason for badge assignment..."
                />
              </div>
            </div>
          </div>

          {/* Current Badges */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Current Badges ({userBadges.length})</h3>
            {userBadges.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No badges assigned yet.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {userBadges.map((badge) => {
                  const daysRemaining = badge.expiry_date ? calculateDaysRemaining(badge.expiry_date) : -1;
                  return (
                    <div
                      key={badge.id}
                      className="border rounded-lg p-4 flex items-start justify-between"
                    >
                      <div className="flex items-center space-x-3">
                        <BadgeImage
                          badge={badge}
                          size="md"
                          showTooltip={false}
                          awardedDate={badge.assigned_date}
                        />
                        <div>
                          <h4 className="font-medium">{badge.badge_type?.name}</h4>
                          <p className="text-sm text-gray-600">{badge.badge_type?.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Calendar size={14} className="text-gray-400" />
                            <span className="text-xs text-gray-500">
                              Assigned: {new Date(badge.assigned_date).toLocaleDateString()}
                            </span>
                          </div>
                          {daysRemaining >= 0 && (
                            <p className={`text-sm ${daysRemaining < 30 ? 'text-yellow-600' : 'text-gray-500'}`}>
                              {daysRemaining} days remaining
                              {daysRemaining < 30 && ' ⚠️'}
                            </p>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveBadge(badge.id, badge.badge_type?.name || 'Unknown Badge')}
                        className="text-red-500 hover:text-red-700 p-1"
                        title="Revoke badge"
                      >
                        <X size={20} />
                      </button>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Available Badges */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Available Badges</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {availableBadges.map((badge) => {
                const isAssigned = userBadges.some(ub => ub.badge_id === badge.id && ub.status === 'active');
                return (
                  <div
                    key={badge.id}
                    className={`border rounded-lg p-4 ${isAssigned ? 'bg-gray-50 border-gray-300' : 'border-gray-200'}`}
                  >
                    <div className="flex items-center space-x-3">
                      <BadgeImage
                        badge={badge}
                        size="md"
                        showTooltip={false}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{badge.name}</h4>
                        <p className="text-sm text-gray-500">{badge.description}</p>
                        {badge.name.includes('Professional') && (
                          <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-1">
                            Professional Badge
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => handleAssignBadge(badge.id)}
                        className={`p-2 rounded-full ${
                          isAssigned
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                        disabled={isAssigned}
                        title={isAssigned ? 'Already assigned' : 'Assign badge'}
                      >
                        {isAssigned ? <X size={20} /> : <Plus size={20} />}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BadgeManagement; 