import React, { useState, useEffect } from 'react';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Briefcase, 
  Edit3, 
  Save, 
  X,
  Upload,
  FileText,
  Download
} from 'lucide-react';
import { hrmsService } from '../../services/hrmsService';
import ProfilePicture from '../ProfilePicture';
import { 
  Employee, 
  EmployeeDocument, 
  EmployeeFormData,
  EMPLOYMENT_TYPES,
  GENDER_OPTIONS,
  MARITAL_STATUS_OPTIONS 
} from '../../types/hrms';
import '../../styles/EmployeeManagement.css';

type PreviousEmployment = {
  company_name: string;
  role: string;
  start_date: string;
  end_date: string;
  reason_for_leaving: string;
};

type EducationEntry = {
  degree: string;
  institution: string;
  year: string;
};

interface EmployeeWithExtras extends Employee {
  previous_employment?: PreviousEmployment[];
  education?: EducationEntry[];
  skills?: string[];
  total_experience?: string;
}

interface EmployeeFormDataWithExtras extends EmployeeFormData {
  previous_employment?: PreviousEmployment[];
  education?: EducationEntry[];
  skills?: string[];
  total_experience?: string;
}

interface EmployeeProfileProps {
  employeeId?: string; // If not provided, shows current user's profile
  isEditable?: boolean;
}

const EmployeeProfile: React.FC<EmployeeProfileProps> = ({ 
  employeeId, 
  isEditable = true 
}) => {
  const [employee, setEmployee] = useState<EmployeeWithExtras | null>(null);
  const [documents, setDocuments] = useState<EmployeeDocument[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<EmployeeFormDataWithExtras>({
    first_name: '',
    last_name: '',
    email: '',
    employee_id: '',
    joining_date: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const [employmentHistory, setEmploymentHistory] = useState<PreviousEmployment[]>([]);
  const [educationHistory, setEducationHistory] = useState<EducationEntry[]>([]);
  const [skills, setSkills] = useState<string[]>([]);
  const [totalExperience, setTotalExperience] = useState<string>('');

  useEffect(() => {
    loadEmployeeData();
  }, [employeeId]);

  useEffect(() => {
    if (employee) {
      setEmploymentHistory(employee.previous_employment || []);
      setEducationHistory(employee.education || []);
      setSkills(employee.skills || []);
      setTotalExperience(employee.total_experience || '');
    }
  }, [employee]);

  const loadEmployeeData = async () => {
    try {
      setLoading(true);
      
      let employeeData: Employee | null;
      if (employeeId) {
        employeeData = await hrmsService.getEmployeeById(employeeId);
      } else {
        employeeData = await hrmsService.getCurrentEmployee();
      }
      
      setEmployee(employeeData);
      
      if (employeeData) {
        setFormData({
          first_name: employeeData.first_name,
          last_name: employeeData.last_name,
          email: employeeData.email,
          phone: employeeData.phone || '',
          date_of_birth: employeeData.date_of_birth || '',
          gender: employeeData.gender || '',
          marital_status: employeeData.marital_status || '',
          nationality: employeeData.nationality || '',
          employee_id: employeeData.employee_id,
          designation: employeeData.designation || '',
          department: employeeData.department || '',
          team_id: employeeData.team_id || '', // Use team_id instead of team
          location: employeeData.location || '',
          employment_type: employeeData.employment_type || '',
          joining_date: employeeData.joining_date,
          personal_email: employeeData.personal_email || '',
          emergency_contact_name: employeeData.emergency_contact_name || '',
          emergency_contact_phone: employeeData.emergency_contact_phone || '',
          emergency_contact_relationship: employeeData.emergency_contact_relationship || '',
          address_line1: employeeData.address_line1 || '',
          address_line2: employeeData.address_line2 || '',
          city: employeeData.city || '',
          state: employeeData.state || '',
          postal_code: employeeData.postal_code || '',
          country: employeeData.country || ''
        });

        // Load documents
        const docs = await hrmsService.getEmployeeDocuments(employeeData.id);
        setDocuments(docs);
      }
    } catch (error) {
      console.error('Error loading employee data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!employee) return;

    try {
      setSaving(true);

      // Only send fields that exist in the employees table
      const updateData = {
        ...formData,
        skills, // This field exists in the database as an array
        // Note: previous_employment, education, and total_experience
        // would need separate tables to be properly stored
        // For now, we'll skip these fields to prevent the 400 error
      };

      console.log('Saving employee data:', updateData);

      await hrmsService.updateEmployee(employee.id, updateData);
      setIsEditing(false);
      await loadEmployeeData(); // Refresh data
    } catch (error: any) {
      console.error('Error saving employee data:', error);
      // Show user-friendly error message
      const errorMessage = error.message || 'Failed to save employee data. Please check the console for details.';
      alert(`Error: ${errorMessage}`);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data
    if (employee) {
      setFormData({
        first_name: employee.first_name,
        last_name: employee.last_name,
        email: employee.email,
        phone: employee.phone || '',
        date_of_birth: employee.date_of_birth || '',
        gender: employee.gender || '',
        marital_status: employee.marital_status || '',
        nationality: employee.nationality || '',
        employee_id: employee.employee_id,
        designation: employee.designation || '',
        department: employee.department || '',
        team_id: employee.team_id || '', // Use team_id instead of team
        location: employee.location || '',
        employment_type: employee.employment_type || '',
        joining_date: employee.joining_date,
        personal_email: employee.personal_email || '',
        emergency_contact_name: employee.emergency_contact_name || '',
        emergency_contact_phone: employee.emergency_contact_phone || '',
        emergency_contact_relationship: employee.emergency_contact_relationship || '',
        address_line1: employee.address_line1 || '',
        address_line2: employee.address_line2 || '',
        city: employee.city || '',
        state: employee.state || '',
        postal_code: employee.postal_code || '',
        country: employee.country || ''
      });
    }
  };

  const handleDocumentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !employee) return;

    try {
      setUploadingDocument(true);
      const documentType = 'OTHER'; // You can add a dropdown to select document type
      await hrmsService.uploadDocument(file, employee.id, documentType);
      await loadEmployeeData(); // Refresh documents
    } catch (error) {
      console.error('Error uploading document:', error);
    } finally {
      setUploadingDocument(false);
    }
  };

  const handleAddEmployment = () => setEmploymentHistory([...employmentHistory, { company_name: '', role: '', start_date: '', end_date: '', reason_for_leaving: '' }]);
  const handleEditEmployment = (idx: number, field: keyof PreviousEmployment, value: string) => setEmploymentHistory(employmentHistory.map((job, i) => i === idx ? { ...job, [field]: value } : job));
  const handleDeleteEmployment = (idx: number) => setEmploymentHistory(employmentHistory.filter((_, i) => i !== idx));

  const handleAddEducation = () => setEducationHistory([...educationHistory, { degree: '', institution: '', year: '' }]);
  const handleEditEducation = (idx: number, field: keyof EducationEntry, value: string) => setEducationHistory(educationHistory.map((edu, i) => i === idx ? { ...edu, [field]: value } : edu));
  const handleDeleteEducation = (idx: number) => setEducationHistory(educationHistory.filter((_, i) => i !== idx));

  const handleAddSkill = () => setSkills([...skills, '']);
  const handleEditSkill = (idx: number, value: string) => setSkills(skills.map((s, i) => i === idx ? value : s));
  const handleDeleteSkill = (idx: number) => setSkills(skills.filter((_, i) => i !== idx));

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Employee not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <ProfilePicture
              userId={employee.user_id || employee.id}
              name={employee.full_name || `${employee.first_name} ${employee.last_name}`}
              email={employee.email}
              profilePictureUrl={employee.profile_picture_url}
              size="lg"
              className="border-2 border-blue-200 shadow-lg"
            />
            <div>
              <h1 className="text-2xl font-black gradient-text header-section animate-scaleIn">
                {employee.first_name} {employee.last_name}
              </h1>
              <p className="text-gray-600">{employee.designation} • {employee.department}</p>
              <p className="text-sm text-gray-500">Employee ID: {employee.employee_id}</p>
            </div>
          </div>
          {isEditable && (
            <div className="flex space-x-2">
              {isEditing ? (
                <>
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    <span>{saving ? 'Saving...' : 'Save'}</span>
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                  >
                    <X className="w-4 h-4" />
                    <span>Cancel</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Edit3 className="w-4 h-4" />
                  <span>Edit Profile</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Personal Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.first_name}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.last_name}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            {isEditing ? (
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.email}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.phone || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
            {isEditing ? (
              <input
                type="date"
                value={formData.date_of_birth}
                onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">
                {employee.date_of_birth ? new Date(employee.date_of_birth).toLocaleDateString() : 'Not provided'}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
            {isEditing ? (
              <select
                value={formData.gender}
                onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Gender</option>
                {GENDER_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.gender || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Job Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <Briefcase className="w-5 h-5 mr-2" />
          Job Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Designation</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.designation}
                onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.designation || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.department || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Team</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.team_id || ''}
                onChange={(e) => setFormData({ ...formData, team_id: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Team ID (UUID)"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.team_id || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Employment Type</label>
            {isEditing ? (
              <select
                value={formData.employment_type}
                onChange={(e) => setFormData({ ...formData, employment_type: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Type</option>
                {EMPLOYMENT_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.employment_type || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Documents */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Documents
          </h2>
          {isEditable && (
            <label className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 cursor-pointer">
              <Upload className="w-4 h-4" />
              <span>{uploadingDocument ? 'Uploading...' : 'Upload Document'}</span>
              <input
                type="file"
                onChange={handleDocumentUpload}
                className="hidden"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                disabled={uploadingDocument}
              />
            </label>
          )}
        </div>
        <div className="space-y-3">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium">{doc.document_name}</p>
                  <p className="text-sm text-gray-500">{doc.document_type}</p>
                </div>
              </div>
              <a
                href={doc.file_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-600 flex items-center space-x-1"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </a>
            </div>
          ))}
          {documents.length === 0 && (
            <p className="text-gray-500 text-center py-4">No documents uploaded</p>
          )}
        </div>
      </div>

    {/* Previous Employment History */}
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4 flex items-center">
        <Briefcase className="w-5 h-5 mr-2" />
        Previous Employment
      </h2>
      <div className="space-y-3">
          {isEditing ? (
            <>
              {employmentHistory.map((job, idx) => (
                <div key={idx} className="p-3 border border-gray-200 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                  <input type="text" placeholder="Company Name" value={job.company_name} onChange={e => handleEditEmployment(idx, 'company_name', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="text" placeholder="Role" value={job.role} onChange={e => handleEditEmployment(idx, 'role', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="date" placeholder="Start Date" value={job.start_date} onChange={e => handleEditEmployment(idx, 'start_date', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="date" placeholder="End Date" value={job.end_date} onChange={e => handleEditEmployment(idx, 'end_date', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="text" placeholder="Reason for Leaving" value={job.reason_for_leaving} onChange={e => handleEditEmployment(idx, 'reason_for_leaving', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <button onClick={() => handleDeleteEmployment(idx)} className="text-red-500">Delete</button>
                </div>
              ))}
              <button onClick={handleAddEmployment} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded">Add Employment</button>
            </>
          ) : (
            (employee.previous_employment && employee.previous_employment.length > 0) ? (
          employee.previous_employment.map((job, idx) => (
            <div key={idx} className="p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{job.company_name}</p>
                  <p className="text-sm text-gray-500">{job.role} ({job.start_date} - {job.end_date || 'Present'})</p>
                </div>
                <div className="text-sm text-gray-400">{job.reason_for_leaving || ''}</div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500 text-center py-4">No previous employment history</p>
            )
        )}
      </div>
    </div>

    {/* Professional Experience & Skills */}
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4 flex items-center">
        <Briefcase className="w-5 h-5 mr-2" />
        Professional Experience & Skills
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Total Experience</label>
            {isEditing ? (
              <input type="number" min="0" value={totalExperience} onChange={e => setTotalExperience(e.target.value)} className="w-full p-2 border border-gray-300 rounded-lg" />
            ) : (
          <p className="p-2 bg-gray-50 rounded-lg">{employee.total_experience || 'Not provided'} years</p>
            )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Key Skills</label>
            {isEditing ? (
              <>
                {skills.map((skill, idx) => (
                  <div key={idx} className="flex items-center gap-2 mb-1">
                    <input type="text" value={skill} onChange={e => handleEditSkill(idx, e.target.value)} className="p-2 border rounded w-full" />
                    <button onClick={() => handleDeleteSkill(idx)} className="text-red-500">Delete</button>
                  </div>
                ))}
                <button onClick={handleAddSkill} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded">Add Skill</button>
              </>
            ) : (
          <p className="p-2 bg-gray-50 rounded-lg">{employee.skills ? employee.skills.join(', ') : 'Not provided'}</p>
            )}
        </div>
      </div>
    </div>

    {/* Education */}
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4 flex items-center">
        <FileText className="w-5 h-5 mr-2" />
        Education
      </h2>
      <div className="space-y-3">
          {isEditing ? (
            <>
              {educationHistory.map((edu, idx) => (
                <div key={idx} className="p-3 border border-gray-200 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                  <input type="text" placeholder="Degree" value={edu.degree} onChange={e => handleEditEducation(idx, 'degree', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="text" placeholder="Institution" value={edu.institution} onChange={e => handleEditEducation(idx, 'institution', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <input type="text" placeholder="Year" value={edu.year} onChange={e => handleEditEducation(idx, 'year', e.target.value)} className="p-2 border rounded mb-1 md:mb-0" />
                  <button onClick={() => handleDeleteEducation(idx)} className="text-red-500">Delete</button>
                </div>
              ))}
              <button onClick={handleAddEducation} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded">Add Education</button>
            </>
          ) : (
            (employee.education && employee.education.length > 0) ? (
          employee.education.map((edu, idx) => (
            <div key={idx} className="p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{edu.degree}</p>
                  <p className="text-sm text-gray-500">{edu.institution} ({edu.year})</p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500 text-center py-4">No education details</p>
            )
        )}
      </div>
    </div>
  </div>
  );
};

export default EmployeeProfile;

