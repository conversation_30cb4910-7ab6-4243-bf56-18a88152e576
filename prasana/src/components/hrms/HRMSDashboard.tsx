import React, { useState, useEffect } from 'react';
import {
  Clock,
  Calendar,
  Users,
  FileText,
  TrendingUp,
  Bell,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  MapPin,
  Coffee
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { hrmsService } from '../../services/hrmsService';
import ProfilePicture from '../ProfilePicture';
import {
  Employee,
  LeaveApplication,
  EmployeeLeaveBalance,
  Notification
} from '../../types/hrms';

interface DashboardStats {
  totalEmployees: number;
  pendingLeaves: number;
  approvedLeaves: number;
  totalLeaveBalance: number;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  color: string;
}

interface HRMSDashboardProps {
  onNavigate?: (page: string) => void;
}

const HRMSDashboard: React.FC<HRMSDashboardProps> = ({ onNavigate }) => {
  const {
    currentEmployee,
    leaveBalance,
    notifications,
    loading: contextLoading,
    refreshEmployeeData
  } = useHRMS();

  const [recentLeaves, setRecentLeaves] = useState<LeaveApplication[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalEmployees: 1,
    pendingLeaves: 0,
    approvedLeaves: 0,
    totalLeaveBalance: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentEmployee) {
      loadDashboardData();
    }
  }, [currentEmployee]);

  const loadDashboardData = async () => {
    if (!currentEmployee) return;

    try {
      setLoading(true);

      // Load recent leave applications
      const leaves = await hrmsService.getLeaveApplications({
        employee_id: currentEmployee.id
      });
      setRecentLeaves(leaves.slice(0, 5));

      // Calculate dashboard stats
      const pendingLeaves = leaves.filter(l => l.status === 'pending').length;
      const approvedLeaves = leaves.filter(l => l.status === 'approved').length;
      const totalLeaveBalance = leaveBalance.reduce((sum, lb) => sum + lb.available_days, 0);

      setDashboardStats({
        totalEmployees: 1,
        pendingLeaves,
        approvedLeaves,
        totalLeaveBalance
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };



  const quickActions: QuickAction[] = [
    {
      id: 'apply-leave',
      title: 'Apply Leave',
      description: 'Submit a new leave request',
      icon: <Calendar className="w-6 h-6" />,
      action: () => onNavigate?.('leave'),
      color: 'bg-blue-500'
    },
    {
      id: 'timesheet',
      title: 'Add Timesheet',
      description: 'Log your work hours',
      icon: <Clock className="w-6 h-6" />,
      action: () => onNavigate?.('timesheet'),
      color: 'bg-green-500'
    },
    {
      id: 'update-profile',
      title: 'Update Profile',
      description: 'Edit personal information',
      icon: <User className="w-6 h-6" />,
      action: () => onNavigate?.('profile'),
      color: 'bg-purple-500'
    },
    {
      id: 'performance',
      title: 'Set Goals',
      description: 'Track performance and goals',
      icon: <TrendingUp className="w-6 h-6" />,
      action: () => onNavigate?.('performance'),
      color: 'bg-orange-500'
    },
    {
      id: 'documents',
      title: 'Upload Documents',
      description: 'Manage personal documents',
      icon: <FileText className="w-6 h-6" />,
      action: () => onNavigate?.('documents'),
      color: 'bg-indigo-500'
    }
  ];

  if (contextLoading || loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!currentEmployee) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Unable to load employee data</p>
          <button
            onClick={refreshEmployeeData}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center space-x-4">
          <ProfilePicture
            userId={currentEmployee?.user_id || currentEmployee?.id}
            name={currentEmployee?.full_name || `${currentEmployee?.first_name} ${currentEmployee?.last_name}`}
            email={currentEmployee?.email}
            profilePictureUrl={currentEmployee?.profile_picture_url}
            size="lg"
            className="ring-4 ring-white ring-opacity-30 shadow-lg"
          />
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {currentEmployee?.first_name}! 👋
            </h1>
            <p className="text-blue-100">
              {currentEmployee?.designation} • {currentEmployee?.department}
            </p>
          </div>
        </div>
      </div>

      {/* Leave Summary Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-blue-100 p-3 rounded-full">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Leave Summary</h3>
              <p className="text-gray-600">
                Total available leave days: {dashboardStats.totalLeaveBalance}
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => onNavigate?.('leave')}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg flex items-center space-x-2"
            >
              <Calendar className="w-4 h-4" />
              <span>Apply Leave</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Leave Balance</p>
              <p className="text-2xl font-bold text-gray-900">
                {leaveBalance.reduce((sum, lb) => sum + lb.available_days, 0)}
              </p>
              <p className="text-gray-500 text-sm">Days Available</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <Calendar className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Pending Leaves</p>
              <p className="text-2xl font-bold text-gray-900">
                {dashboardStats.pendingLeaves}
              </p>
              <p className="text-gray-500 text-sm">Awaiting Approval</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Approved Leaves</p>
              <p className="text-2xl font-bold text-gray-900">
                {dashboardStats.approvedLeaves}
              </p>
              <p className="text-gray-500 text-sm">This Year</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Profile Status</p>
              <p className="text-2xl font-bold text-gray-900">
                Active
              </p>
              <p className="text-gray-500 text-sm">Employee Status</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <User className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-left"
            >
              <div className={`${action.color} w-10 h-10 rounded-lg flex items-center justify-center text-white mb-3`}>
                {action.icon}
              </div>
              <h4 className="font-medium text-gray-900">{action.title}</h4>
              <p className="text-sm text-gray-600">{action.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Leave Balance & Recent Applications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Leave Balance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Leave Balance</h3>
          <div className="space-y-3">
            {leaveBalance.map((balance) => (
              <div key={balance.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{balance.leave_type?.name}</p>
                  <p className="text-sm text-gray-600">
                    Used: {balance.used_days} / {balance.allocated_days}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600">{balance.available_days}</p>
                  <p className="text-xs text-gray-500">Available</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Leave Applications */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Leave Applications</h3>
          <div className="space-y-3">
            {recentLeaves.map((leave) => (
              <div key={leave.id} className="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                <div>
                  <p className="font-medium">{leave.leave_type?.name}</p>
                  <p className="text-sm text-gray-600">
                    {new Date(leave.start_date).toLocaleDateString()} - {new Date(leave.end_date).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    leave.status === 'approved' ? 'bg-green-100 text-green-800' :
                    leave.status === 'rejected' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HRMSDashboard;
