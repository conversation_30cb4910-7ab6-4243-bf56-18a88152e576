import { supabase } from '../supabaseClient';
import { teams } from '../data/teams';
import {
  Employee,
  EmployeeDocument,
  Attendance,
  LeaveType,
  EmployeeLeaveBalance,
  LeaveApplication,
  Holiday,
  Payroll,
  PerformanceReview,
  SelfServiceRequest,
  Notification,
  EmployeeFormData,
  LeaveApplicationFormData,
  AttendanceFormData,
  EmployeeFilters,
  AttendanceFilters,
  LeaveFilters,
  PaginatedResponse,
  AttendanceSummary
} from '../types/hrms';

// Utility function to get avatar from teams data
const getAvatarFromTeams = (name: string, email?: string): string | null => {
  const normalizedName = name.toLowerCase().trim();
  const firstName = normalizedName.split(' ')[0];

  // Search through all teams for matching member
  for (const teamData of Object.values(teams)) {
    // Check SDM, TDM, CXM
    const leaders = [teamData.sdm, teamData.tdm, teamData.cxm];
    for (const leader of leaders) {
      const leaderName = leader.name.toLowerCase().trim();

      // Exact match or first name match
      if (leaderName === normalizedName || leaderName === firstName || leaderName.startsWith(firstName + ' ')) {
        return leader.avatar || null;
      }
    }

    // Check team members
    for (const member of teamData.members) {
      const memberName = member.name.toLowerCase().trim();

      // Exact match or first name match
      if (memberName === normalizedName || memberName === firstName || memberName.startsWith(firstName + ' ')) {
        return member.avatar || null;
      }
    }
  }

  // Flexible partial matching
  for (const teamData of Object.values(teams)) {
    const allMembers = [teamData.sdm, teamData.tdm, teamData.cxm, ...teamData.members];
    for (const member of allMembers) {
      const memberName = member.name.toLowerCase().trim();

      // Check if names contain each other
      if (memberName.includes(firstName) || firstName.includes(memberName.split(' ')[0])) {
        return member.avatar || null;
      }
    }
  }

  return null;
};

class HRMSService {
  // Employee Management
  async getEmployees(filters?: EmployeeFilters): Promise<Employee[]> {
    let query = supabase
      .from('employees')
      .select(`
        *,
        manager:manager_id(id, first_name, last_name, designation)
      `);

    if (filters?.department) {
      query = query.eq('department', filters.department);
    }
    if (filters?.team) {
      query = query.eq('team', filters.team);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.employment_type) {
      query = query.eq('employment_type', filters.employment_type);
    }
    if (filters?.search) {
      query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
    }

    const { data, error } = await query.order('first_name');
    if (error) throw error;
    
    return data?.map(emp => ({
      ...emp,
      full_name: `${emp.first_name} ${emp.last_name}`,
      profile_picture_url: emp.profile_picture_url || getAvatarFromTeams(`${emp.first_name} ${emp.last_name}`, emp.email)
    })) || [];
  }

  async getEmployeeById(id: string): Promise<Employee | null> {
    const { data, error } = await supabase
      .from('employees')
      .select(`
        *,
        manager:manager_id(id, first_name, last_name, designation)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    
    return data ? {
      ...data,
      full_name: `${data.first_name} ${data.last_name}`,
      profile_picture_url: data.profile_picture_url || getAvatarFromTeams(`${data.first_name} ${data.last_name}`, data.email)
    } : null;
  }

  async getCurrentEmployee(): Promise<Employee | null> {
    try {
      console.log('🔍 Getting current employee...');
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('❌ No authenticated user found');
        return null;
      }

      console.log('👤 User found:', user.id, user.email);

      // Simple query without complex joins
      let { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('user_id', user.id)
        .single();

      console.log('🔍 Employee query result:', { data, error });

      // If no employee record exists, create one from the auth user
      if (error && error.code === 'PGRST116') { // No rows returned
        console.log('📝 Creating new employee record...');
        const email = user.email || '';
        const name = user.user_metadata?.name || email.split('@')[0];
        const [firstName, ...lastNameParts] = name.split(' ');
        const lastName = lastNameParts.join(' ') || 'User';

        const newEmployee = {
          user_id: user.id,
          employee_id: `EMP${Date.now()}`,
          first_name: firstName,
          last_name: lastName,
          email: email,
          joining_date: new Date().toISOString().split('T')[0],
          status: 'active',
          casual_leave_balance: 12,
          sick_leave_balance: 12,
          annual_leave_balance: 21
        };

        console.log('📝 New employee data:', newEmployee);

        const { data: createdEmployee, error: createError } = await supabase
          .from('employees')
          .insert([newEmployee])
          .select('*')
          .single();

        console.log('📝 Employee creation result:', { createdEmployee, createError });

        if (createError) {
          console.error('❌ Error creating employee:', createError);
          throw createError;
        }
        data = createdEmployee;
      } else if (error) {
        console.error('❌ Error fetching employee:', error);
        throw error;
      }

      const result = data ? {
        ...data,
        full_name: `${data.first_name} ${data.last_name}`,
        profile_picture_url: data.profile_picture_url || getAvatarFromTeams(`${data.first_name} ${data.last_name}`, data.email)
      } : null;

      console.log('✅ Final employee result:', result);
      return result;
    } catch (error) {
      console.error('❌ getCurrentEmployee error:', error);
      throw error;
    }
  }

  async createEmployee(employeeData: EmployeeFormData): Promise<Employee> {
    const { data, error } = await supabase
      .from('employees')
      .insert([employeeData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateEmployee(id: string, employeeData: Partial<EmployeeFormData>): Promise<Employee> {
    const { data, error } = await supabase
      .from('employees')
      .update({ ...employeeData, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Document Management
  async getEmployeeDocuments(employeeId: string): Promise<EmployeeDocument[]> {
    const { data, error } = await supabase
      .from('employee_documents')
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async uploadDocument(file: File, employeeId: string, documentType: string): Promise<EmployeeDocument> {
    // Upload file to Supabase Storage
    const fileExt = file.name.split('.').pop();
    const fileName = `${employeeId}/${documentType}_${Date.now()}.${fileExt}`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('employee-documents')
      .upload(fileName, file);

    if (uploadError) throw uploadError;

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('employee-documents')
      .getPublicUrl(fileName);

    // Save document record
    const { data, error } = await supabase
      .from('employee_documents')
      .insert([{
        employee_id: employeeId,
        document_type: documentType,
        document_name: file.name,
        file_url: publicUrl,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: employeeId
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }



  // Leave Management - Simplified
  async getLeaveTypes(): Promise<LeaveType[]> {
    // Return hardcoded leave types instead of database query
    return [
      {
        id: '1',
        name: 'Casual Leave',
        code: 'CL',
        description: 'Casual leave for personal reasons',
        max_days_per_year: 12,
        carry_forward_allowed: true,
        requires_approval: true,
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Sick Leave',
        code: 'SL',
        description: 'Medical leave for illness',
        max_days_per_year: 12,
        carry_forward_allowed: false,
        requires_approval: true,
        created_at: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Annual Leave',
        code: 'AL',
        description: 'Annual vacation leave',
        max_days_per_year: 21,
        carry_forward_allowed: true,
        requires_approval: true,
        created_at: new Date().toISOString()
      }
    ];
  }

  async getLeaveBalance(employeeId: string, year?: number): Promise<EmployeeLeaveBalance[]> {
    try {
      // Get employee data to read leave balance from text fields
      const { data: employee, error } = await supabase
        .from('employees')
        .select('casual_leave_balance, sick_leave_balance, annual_leave_balance')
        .eq('id', employeeId)
        .single();

      if (error) throw error;

      // Convert text fields to leave balance objects
      const leaveTypes = await this.getLeaveTypes();
      const currentYear = year || new Date().getFullYear();

      return leaveTypes.map(leaveType => {
        let availableDays = 0;
        if (leaveType.code === 'CL') availableDays = parseInt(employee.casual_leave_balance || '12');
        if (leaveType.code === 'SL') availableDays = parseInt(employee.sick_leave_balance || '12');
        if (leaveType.code === 'AL') availableDays = parseInt(employee.annual_leave_balance || '21');

        return {
          id: `${employeeId}-${leaveType.id}-${currentYear}`,
          employee_id: employeeId,
          leave_type_id: leaveType.id,
          year: currentYear,
          allocated_days: leaveType.max_days_per_year || 0,
          used_days: (leaveType.max_days_per_year || 0) - availableDays,
          carried_forward: 0,
          available_days: availableDays,
          leave_type: leaveType,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      });
    } catch (error) {
      console.error('Error getting leave balance:', error);
      return [];
    }
  }

  async applyLeave(leaveData: LeaveApplicationFormData & { employee_id: string }): Promise<any> {
    try {
      // Calculate total days
      const startDate = new Date(leaveData.start_date);
      const endDate = new Date(leaveData.end_date);
      const totalDays = this.calculateLeaveDays(startDate, endDate);

      const { data, error } = await supabase
        .from('leave_applications')
        .insert([{
          employee_id: leaveData.employee_id,
          leave_type: leaveData.leave_type_id, // Store as text
          start_date: leaveData.start_date,
          end_date: leaveData.end_date,
          total_days: totalDays.toString(),
          reason: leaveData.reason || '',
          status: 'pending',
          applied_date: new Date().toISOString().split('T')[0]
        }])
        .select('*')
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error applying for leave:', error);
      throw error;
    }
  }

  async getLeaveApplications(filters?: LeaveFilters): Promise<any[]> {
    try {
      let query = supabase
        .from('leave_applications')
        .select('*');

      if (filters?.employee_id) {
        query = query.eq('employee_id', filters.employee_id);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.start_date) {
        query = query.gte('start_date', filters.start_date);
      }
      if (filters?.end_date) {
        query = query.lte('end_date', filters.end_date);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      if (error) throw error;

      // Convert to proper format
      return (data || []).map(app => ({
        ...app,
        total_days: parseFloat(app.total_days || '0'),
        leave_type: {
          name: app.leave_type,
          code: app.leave_type
        }
      }));
    } catch (error) {
      console.error('Error getting leave applications:', error);
      return [];
    }
  }

  async approveLeaveApplication(applicationId: string, approverId: string): Promise<LeaveApplication> {
    const { data, error } = await supabase
      .from('leave_applications')
      .update({
        status: 'approved',
        approved_by: approverId,
        approved_date: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async rejectLeaveApplication(applicationId: string, approverId: string, reason: string): Promise<LeaveApplication> {
    const { data, error } = await supabase
      .from('leave_applications')
      .update({
        status: 'rejected',
        approved_by: approverId,
        approved_date: new Date().toISOString(),
        rejection_reason: reason
      })
      .eq('id', applicationId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Notification Management
  async getNotifications(employeeId: string): Promise<Notification[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select(`
        *,
        sender:sender_id(first_name, last_name)
      `)
      .eq('recipient_id', employeeId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) throw error;
    return data || [];
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ read_status: true })
      .eq('id', notificationId);

    if (error) throw error;
  }

  async createNotification(notification: Omit<Notification, 'id' | 'created_at'>): Promise<Notification> {
    const { data, error } = await supabase
      .from('notifications')
      .insert([notification])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Holiday Management
  async getHolidays(year?: number): Promise<Holiday[]> {
    const currentYear = year || new Date().getFullYear();

    const { data, error } = await supabase
      .from('holidays')
      .select('*')
      .gte('date', `${currentYear}-01-01`)
      .lte('date', `${currentYear}-12-31`)
      .order('date');

    if (error) throw error;
    return data || [];
  }

  // Dashboard Data
  async getDashboardStats(employeeId: string): Promise<any> {
    // This would aggregate various stats for the dashboard
    // Implementation depends on specific requirements
    const today = new Date().toISOString().split('T')[0];
    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0];

    // Get today's attendance
    const todayAttendance = await this.getAttendance({
      employee_id: employeeId,
      start_date: today,
      end_date: today
    });

    // Get month's attendance summary
    const monthSummary = await this.getAttendanceSummary(employeeId, startOfMonth, endOfMonth);

    // Get leave balance
    const leaveBalance = await this.getLeaveBalance(employeeId);

    // Get pending leave applications
    const pendingLeaves = await this.getLeaveApplications({
      employee_id: employeeId,
      status: 'pending'
    });

    return {
      todayAttendance: todayAttendance[0] || null,
      monthSummary,
      leaveBalance,
      pendingLeaves: pendingLeaves.length
    };
  }



  // Utility methods
  private getWorkingDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dayOfWeek = d.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
        workingDays++;
      }
    }

    return workingDays;
  }

  private calculateLeaveDays(startDate: Date, endDate: Date): number {
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
  }
}

export const hrmsService = new HRMSService();
