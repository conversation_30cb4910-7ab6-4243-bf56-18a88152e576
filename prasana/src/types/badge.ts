export interface BadgeType {
  id: string;
  name: string;
  description: string;
  skills: string[];
  image_path: string;
  color: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmployeeBadge {
  id: string;
  employee_id: string;
  badge_id: string;
  assigned_by: string;
  assigned_date: string;
  expiry_date?: string;
  status: 'active' | 'expired' | 'revoked';
  revoked_by?: string;
  revoked_date?: string;
  revocation_reason?: string;
  notes?: string;
  achievement_data?: any;
  created_at: string;
  updated_at: string;
  // Joined data
  badge_type?: BadgeType;
  assigned_by_employee?: {
    first_name: string;
    last_name: string;
  };
}

export interface BadgeAssignment {
  employee_id: string;
  badge_type_id: string;
  notes?: string;
  expiry_date?: string;
}

export interface BadgeStats {
  total_badges: number;
  active_badges: number;
  expired_badges: number;
  revoked_badges: number;
  badges_by_type: {
    [key: string]: number;
  };
}

// Predefined badge types for easy reference
export const BADGE_TYPES = {
  IT_SUPPORT: 'IT Support Professional',
  WEB_DEVELOPMENT: 'Web Development Professional',
  OFFICE_365: 'Office 365 Professional',
  FULL_STACK: 'Full Stack Development',
  CLIENT_ACQUISITION: 'Client Acquisition',
  DIGITAL_MARKETING: 'Digital Marketing Professional',
  DATA_ANALYSIS: 'Data Analysis Professional',
  CLOUD_DEPLOYMENT: 'Cloud Deployment Specialist'
} as const;

export type BadgeTypeName = typeof BADGE_TYPES[keyof typeof BADGE_TYPES];
