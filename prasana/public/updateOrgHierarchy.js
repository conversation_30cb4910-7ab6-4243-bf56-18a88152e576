// Simple script to update organizational hierarchy
// Run this in browser console after navigating to the HRMS application

async function updateOrgHierarchy() {
  console.log('🚀 Starting organizational hierarchy update...');
  
  try {
    // This assumes supabase client is available globally
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // Step 1: Get CEO ID (ARUN G)
    const { data: ceoData, error: ceoError } = await supabase
      .from('employees')
      .select('id')
      .eq('designation', 'CEO')
      .eq('first_name', 'ARUN')
      .single();

    if (ceoError || !ceoData) {
      throw new Error('CEO ARUN G not found');
    }

    const ceoId = ceoData.id;
    console.log('✅ Found CEO:', ceoId);

    // Step 2: Create or update Priyadarshini as CRO
    let { data: croData, error: croError } = await supabase
      .from('employees')
      .select('id, first_name, last_name')
      .ilike('first_name', '%Priyadarshini%')
      .single();

    let croId;

    if (croError || !croData) {
      // Create Priyadarshini as CRO
      console.log('📝 Creating Priyadarshini as CRO...');
      const { data: newCro, error: createError } = await supabase
        .from('employees')
        .insert([{
          user_id: crypto.randomUUID(),
          first_name: 'Priyadarshini',
          last_name: 'M2',
          designation: 'Chief Revenue Officer',
          department: 'REVENUE',
          employee_id: 'EMP_CRO_001',
          employee_code: 'CRO001',
          email: '<EMAIL>',
          status: 'active',
          reports_to: ceoId,
          hire_date: '2023-01-01',
          joining_date: '2023-01-01'
        }])
        .select('id')
        .single();

      if (createError || !newCro) {
        throw new Error('Failed to create CRO: ' + createError?.message);
      }

      croId = newCro.id;
      console.log('✅ Created CRO Priyadarshini:', croId);
    } else {
      // Update existing Priyadarshini to CRO
      croId = croData.id;
      console.log('📝 Updating existing Priyadarshini to CRO...');
      
      const { error: updateError } = await supabase
        .from('employees')
        .update({
          designation: 'Chief Revenue Officer',
          department: 'REVENUE',
          employee_code: 'CRO001',
          reports_to: ceoId
        })
        .eq('id', croId);

      if (updateError) {
        throw new Error('Failed to update CRO: ' + updateError.message);
      }
      console.log('✅ Updated CRO Priyadarshini:', croId);
    }

    // Step 3: Update all SDMs to report to CRO
    console.log('📝 Updating SDMs to report to CRO...');
    const { error: sdmUpdateError } = await supabase
      .from('employees')
      .update({ reports_to: croId })
      .eq('designation', 'Service Delivery Manager');

    if (sdmUpdateError) {
      throw new Error('Failed to update SDMs: ' + sdmUpdateError.message);
    }
    console.log('✅ Updated SDMs to report to CRO');

    // Step 4: Update specific employee movements for team swaps
    console.log('📝 Updating team member assignments...');
    
    // Move Sivaranjani to TITAN
    await supabase
      .from('employees')
      .update({ 
        department: 'TITAN',
        first_name: 'Sivaranjani',
        last_name: 'K'
      })
      .ilike('first_name', '%Sivaranjani%');

    // Move Fazeela to TITAN  
    await supabase
      .from('employees')
      .update({ 
        department: 'TITAN',
        first_name: 'Fazeela',
        last_name: 'M'
      })
      .ilike('first_name', '%Fazeela%');

    console.log('✅ Updated team member assignments');

    // Step 5: Verify the hierarchy
    console.log('🔍 Verifying organizational hierarchy...');
    
    const { data: hierarchyData, error: hierarchyError } = await supabase
      .from('employees')
      .select(`
        id,
        first_name,
        last_name,
        designation,
        department,
        employee_code,
        manager:employees!reports_to(first_name, last_name, designation)
      `)
      .in('department', ['EXECUTIVE', 'REVENUE', 'NEXUS', 'DYNAMIX', 'ATHENA', 'TITAN'])
      .order('designation', { ascending: false });

    if (hierarchyError) {
      console.error('Error verifying hierarchy:', hierarchyError);
    } else {
      console.log('📊 Current Hierarchy:');
      hierarchyData?.forEach(emp => {
        const managerInfo = emp.manager ? `→ ${emp.manager.first_name} ${emp.manager.last_name} (${emp.manager.designation})` : '(Top Level)';
        console.log(`${emp.first_name} ${emp.last_name} (${emp.designation} - ${emp.department}) ${managerInfo}`);
      });
    }

    console.log('🎉 Organizational hierarchy update completed successfully!');
    
    return {
      success: true,
      message: 'Organizational hierarchy updated successfully',
      ceoId,
      croId
    };

  } catch (error) {
    console.error('❌ Error updating organizational hierarchy:', error);
    throw error;
  }
}

// Make function available globally
window.updateOrgHierarchy = updateOrgHierarchy;

console.log('📋 Organizational Hierarchy Updater loaded!');
console.log('💡 Run updateOrgHierarchy() to execute the update');
